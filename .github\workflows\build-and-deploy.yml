name: Build and Deploy

on:
  push:
    branches:
      - main  # 或者您的主分支名称

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0  # 获取完整的 git 历史
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.x'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Clean old build
      run: |
        if [ -d "build" ]; then
          rm -rf build
        fi
    
    - name: Build static files
      run: python build.py
    
    - name: Move sitemap and favicon to build root
      run: |
        mv build/static/sitemap.xml build/
        if [ -f "build/static/favicon.ico" ]; then
          mv build/static/favicon.ico build/
        fi
    
    - name: Deploy to GitHub Pages
      run: |
        git config --global user.name 'GitHub Actions'
        git config --global user.email '<EMAIL>'
        git add build/
        git commit -m "Update static files"
        git push
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 