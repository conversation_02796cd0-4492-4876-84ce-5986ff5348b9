{% extends 'base.html' %}

{% block title %}Free Permit Test Practice 2024 - Pass Your DMV Permit Test{% endblock %}
{% block meta_title %}Free Permit Test Practice 2024 - Pass Your DMV Permit Test{% endblock %}
{% block description %}Take our free permit test practice with real DMV questions. Updated 2024 permit test for all states including NY, FL, CA, PA, OH, MA, KY, TN. Pass your permit test on the first try!{% endblock %}

{% block og_title %}Free Permit Test Practice 2024 - Pass Your DMV Permit Test{% endblock %}
{% block og_description %}Take our free permit test practice with real DMV questions. Updated 2024 permit test for all states. Pass your permit test on the first try!{% endblock %}

{% block twitter_title %}Free Permit Test Practice 2024 - Pass Your DMV Permit Test{% endblock %}
{% block twitter_description %}Take our free permit test practice with real DMV questions. Updated 2024 permit test for all states. Pass your permit test on the first try!{% endblock %}

{% block canonical %}https://permittestpro.com/{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "PermitTestPro",
  "url": "https://permittestpro.com",
  "description": "Free permit test practice with real DMV questions for all states",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://permittestpro.com/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "How many questions are on the permit test?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "The number of questions varies by state. Most permit tests have between 20-50 questions. For example, New York has 20 questions, Florida has 50, and California has 46 questions."
      }
    },
    {
      "@type": "Question",
      "name": "What score do I need to pass the permit test?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Most states require a passing score of 80% or higher. This means you need to answer at least 16 out of 20 questions correctly, or 40 out of 50 questions correctly, depending on your state's test format."
      }
    },
    {
      "@type": "Question",
      "name": "Can I take the permit test online?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Some states offer online permit testing, including New York, Florida, and California. Check with your state's DMV to see if online testing is available in your area."
      }
    }
  ]
}
</script>
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">Permit Test Practice</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<header class="hero-section bg-primary text-white py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold">Free Permit Test Practice - Pass Your DMV Permit Test</h1>
        <p class="lead">Take our comprehensive permit test practice questions and pass your DMV permit test on the first try. Updated 2024 questions for all states.</p>
        <div class="d-flex gap-3 flex-wrap mt-4">
          <a href="#permit-test-tool" class="btn btn-light btn-lg px-4 py-2">Start Practice Test</a>
          <a href="#study-tips" class="btn btn-outline-light btn-lg px-4 py-2">Study Tips</a>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Interactive Permit Test Tool -->
<section id="permit-test-tool" class="permit-test-tool py-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <!-- Test Setup Card -->
        <div class="card shadow-lg" id="test-setup">
          <div class="card-header bg-success text-white">
            <h2 class="h3 mb-0">🚗 Start Your Permit Test Practice Now</h2>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <label for="stateSelect" class="form-label">Select Your State:</label>
                <select class="form-select mb-3" id="stateSelect">
                  <option value="">Choose your state...</option>
                  <option value="ny">New York (NY)</option>
                  <option value="fl">Florida (FL)</option>
                  <option value="ca">California (CA)</option>
                  <option value="tx">Texas (TX)</option>
                  <option value="pa">Pennsylvania (PA)</option>
                  <option value="oh">Ohio (OH)</option>
                  <option value="ma">Massachusetts (MA)</option>
                  <option value="ky">Kentucky (KY)</option>
                  <option value="tn">Tennessee (TN)</option>
                </select>
              </div>
              <div class="col-md-6">
                <label for="testType" class="form-label">Test Type:</label>
                <select class="form-select mb-3" id="testType">
                  <option value="quick">Quick Review (10 questions)</option>
                  <option value="practice">Practice Test (20 questions)</option>
                  <option value="full">Full DMV Test (36 questions)</option>
                </select>
              </div>
            </div>
            <button class="btn btn-primary btn-lg w-100" onclick="startPermitTest()">
              Start Permit Test Practice →
            </button>
          </div>
        </div>

        <!-- Test Interface Card -->
        <div class="card shadow-lg" id="test-interface" style="display: none;">
          <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <div>
              <h3 class="h4 mb-0">Permit Test Practice</h3>
              <small id="test-info">Question 1 of 10</small>
            </div>
            <div class="text-end">
              <div class="badge bg-light text-dark fs-6" id="timer">10:00</div>
              <div class="small mt-1">Score: <span id="current-score">0/0</span></div>
            </div>
          </div>
          <div class="card-body">
            <!-- Progress Bar -->
            <div class="progress mb-4" style="height: 8px;">
              <div class="progress-bar bg-success" id="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>

            <!-- Question Display -->
            <div id="question-container">
              <h4 class="mb-4" id="question-text">Loading question...</h4>
              <div id="question-image" class="text-center mb-4" style="display: none;">
                <img src="" alt="Traffic sign" class="img-fluid" style="max-height: 200px;">
              </div>
              <div id="answers-container">
                <!-- Answer options will be populated here -->
              </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between mt-4">
              <button class="btn btn-outline-secondary" id="prev-btn" onclick="previousQuestion()" disabled>
                ← Previous
              </button>
              <button class="btn btn-outline-danger" onclick="endTest()">
                End Test
              </button>
              <button class="btn btn-primary" id="next-btn" onclick="nextQuestion()" disabled>
                Next →
              </button>
            </div>
          </div>
        </div>

        <!-- Results Card -->
        <div class="card shadow-lg" id="test-results" style="display: none;">
          <div class="card-header text-white" id="results-header">
            <h3 class="h4 mb-0">Test Results</h3>
          </div>
          <div class="card-body">
            <div class="text-center mb-4">
              <div class="display-4 mb-2" id="final-score">85%</div>
              <h4 id="pass-status" class="mb-3">Congratulations! You Passed!</h4>
              <p class="lead" id="score-details">You answered 17 out of 20 questions correctly.</p>
            </div>

            <!-- Performance Breakdown -->
            <div class="row mb-4">
              <div class="col-md-4 text-center">
                <div class="card border-0 bg-light">
                  <div class="card-body">
                    <div class="h3 text-success" id="correct-count">17</div>
                    <small class="text-muted">Correct</small>
                  </div>
                </div>
              </div>
              <div class="col-md-4 text-center">
                <div class="card border-0 bg-light">
                  <div class="card-body">
                    <div class="h3 text-danger" id="incorrect-count">3</div>
                    <small class="text-muted">Incorrect</small>
                  </div>
                </div>
              </div>
              <div class="col-md-4 text-center">
                <div class="card border-0 bg-light">
                  <div class="card-body">
                    <div class="h3 text-primary" id="time-taken">8:45</div>
                    <small class="text-muted">Time Taken</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2 justify-content-center flex-wrap">
              <button class="btn btn-primary" onclick="reviewAnswers()">
                Review Answers
              </button>
              <button class="btn btn-outline-primary" onclick="retakeTest()">
                Take Another Test
              </button>
              <button class="btn btn-outline-success" onclick="shareResults()">
                Share Results
              </button>
            </div>
          </div>
        </div>

        <!-- Answer Review Card -->
        <div class="card shadow-lg" id="answer-review" style="display: none;">
          <div class="card-header bg-info text-white">
            <h3 class="h4 mb-0">Answer Review</h3>
          </div>
          <div class="card-body">
            <div id="review-container">
              <!-- Review content will be populated here -->
            </div>
            <div class="text-center mt-4">
              <button class="btn btn-primary" onclick="retakeTest()">
                Take Another Test
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- What is a Permit Test? Section -->
<section class="what-section py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 mb-4">What is a Permit Test?</h2>
        <p class="lead">A permit test, also known as a DMV permit test or learner's permit test, is a written examination that evaluates your knowledge of traffic laws, road signs, and safe driving practices. This test is required before you can obtain your learner's permit to begin practicing driving.</p>

        <div class="row mt-4">
          <div class="col-md-6">
            <h3 class="h4">Permit Test Components:</h3>
            <ul class="list-unstyled">
              <li>✓ Traffic laws and regulations</li>
              <li>✓ Road signs and signals</li>
              <li>✓ Safe driving practices</li>
              <li>✓ Parking and turning rules</li>
              <li>✓ Alcohol and drug laws</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h3 class="h4">Test Format:</h3>
            <ul class="list-unstyled">
              <li>✓ Multiple choice questions</li>
              <li>✓ 20-50 questions (varies by state)</li>
              <li>✓ 80% passing score required</li>
              <li>✓ Computer-based or paper test</li>
              <li>✓ Available in multiple languages</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Why Take Permit Test Practice? Section -->
<section class="why-section py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 mb-4">Why Should You Practice for Your Permit Test?</h2>
        <p class="lead">Taking permit test practice questions significantly increases your chances of passing the DMV permit test on your first attempt. Our practice tests simulate the real exam experience and help you identify areas that need improvement.</p>

        <div class="row mt-4">
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">📈</div>
                <h3 class="h5">Higher Pass Rates</h3>
                <p>Students who practice score 40% higher on average</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-success mb-3">⏰</div>
                <h3 class="h5">Save Time & Money</h3>
                <p>Avoid retaking fees and multiple DMV visits</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center">
                <div class="display-4 text-info mb-3">🎯</div>
                <h3 class="h5">Build Confidence</h3>
                <p>Feel prepared and confident on test day</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Where Can You Take Your Permit Test? Section -->
<section class="where-section py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 mb-4">Where Can You Take Your DMV Permit Test?</h2>
        <p class="lead">You can take your permit test at various DMV locations and some states now offer online permit test options. Here's everything you need to know about permit test locations and requirements.</p>

        <div class="row mt-4">
          <div class="col-md-6">
            <h3 class="h4">DMV Office Locations:</h3>
            <ul class="list-unstyled">
              <li>✓ Local DMV branches</li>
              <li>✓ Motor vehicle agencies</li>
              <li>✓ Secretary of State offices</li>
              <li>✓ Third-party testing centers</li>
              <li>✓ Some AAA locations</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h3 class="h4">Online Options:</h3>
            <ul class="list-unstyled">
              <li>✓ NY DMV permit test online</li>
              <li>✓ Florida online permit test</li>
              <li>✓ California online testing</li>
              <li>✓ Remote proctored exams</li>
              <li>✓ 24/7 availability</li>
            </ul>
          </div>
        </div>

        <div class="alert alert-info mt-4">
          <h4 class="h5">What to Bring to DMV for Permit Test Over 18:</h4>
          <ul class="mb-0">
            <li>Valid identification (birth certificate, passport)</li>
            <li>Social Security card or W-2</li>
            <li>Proof of residency (utility bills, bank statements)</li>
            <li>Completed application form</li>
            <li>Testing fee (varies by state)</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- State-Specific Permit Test Information -->
<section class="state-tests py-5">
  <div class="container">
    <h2 class="h2 text-center mb-5">Permit Test by State</h2>
    <div class="row">
      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h3 class="h5 card-title">New York Permit Test</h3>
            <p class="card-text">NY DMV permit test practice with updated 2024 questions. Take the NY permit test online or at DMV locations.</p>
            <ul class="list-unstyled small">
              <li>• 20 questions</li>
              <li>• 14 correct to pass</li>
              <li>• Available online</li>
            </ul>
            <a href="#" class="btn btn-outline-primary">NY Practice Test</a>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h3 class="h5 card-title">Florida Permit Test</h3>
            <p class="card-text">Florida permit test practice with official DMV questions. Prepare for your FL permit test with our comprehensive study materials.</p>
            <ul class="list-unstyled small">
              <li>• 50 questions</li>
              <li>• 40 correct to pass</li>
              <li>• Multiple languages</li>
            </ul>
            <a href="#" class="btn btn-outline-primary">FL Practice Test</a>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h3 class="h5 card-title">California Permit Test</h3>
            <p class="card-text">CA permit test practice with real DMV questions. Get ready for your California permit test with our proven study system.</p>
            <ul class="list-unstyled small">
              <li>• 46 questions</li>
              <li>• 38 correct to pass</li>
              <li>• Teen & adult versions</li>
            </ul>
            <a href="#" class="btn btn-outline-primary">CA Practice Test</a>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h3 class="h5 card-title">Pennsylvania Permit Test</h3>
            <p class="card-text">PA permit test practice questions based on the official Pennsylvania driver's manual. Master your permit test PA requirements.</p>
            <ul class="list-unstyled small">
              <li>• 18 questions</li>
              <li>• 15 correct to pass</li>
              <li>• Knowledge test focus</li>
            </ul>
            <a href="#" class="btn btn-outline-primary">PA Practice Test</a>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h3 class="h5 card-title">Ohio Permit Test</h3>
            <p class="card-text">Ohio permit test practice with current BMV questions. Prepare for your OH permit test with our comprehensive study guide.</p>
            <ul class="list-unstyled small">
              <li>• 40 questions</li>
              <li>• 30 correct to pass</li>
              <li>• Road signs included</li>
            </ul>
            <a href="#" class="btn btn-outline-primary">OH Practice Test</a>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
          <div class="card-body">
            <h3 class="h5 card-title">More States</h3>
            <p class="card-text">Find permit test practice for Massachusetts, Kentucky, Tennessee, and all other states.</p>
            <ul class="list-unstyled small">
              <li>• All 50 states</li>
              <li>• Updated questions</li>
              <li>• Official requirements</li>
            </ul>
            <a href="#" class="btn btn-outline-primary">View All States</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Permit Test Study Tips & Resources -->
<section id="study-tips" class="study-tips py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 text-center mb-5">How to Pass Your Permit Test on the First Try</h2>

        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-primary">📚 Study the Manual</h3>
                <p>Read your state's official driver's manual thoroughly. Focus on traffic laws, road signs, and safety rules specific to your state.</p>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-success">🎯 Take Practice Tests</h3>
                <p>Use our permit test practice questions to simulate the real exam. Take multiple practice tests until you consistently score 90% or higher.</p>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-info">🚦 Learn Road Signs</h3>
                <p>Memorize all traffic signs, signals, and road markings. Many permit test questions focus on sign recognition and meaning.</p>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <h3 class="h5 text-warning">⏰ Schedule Wisely</h3>
                <p>Book your DMV permit test appointment in advance. Arrive early and bring all required documents to avoid delays.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Frequently Asked Questions -->
<section class="faq py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="h2 text-center mb-5">Permit Test FAQ</h2>

        <div class="accordion" id="permitTestFAQ">
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                How many questions are on the permit test?
              </button>
            </h3>
            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#permitTestFAQ">
              <div class="accordion-body">
                The number of questions varies by state. Most permit tests have between 20-50 questions. For example, New York has 20 questions, Florida has 50, and California has 46 questions.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                What score do I need to pass the permit test?
              </button>
            </h3>
            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#permitTestFAQ">
              <div class="accordion-body">
                Most states require a passing score of 80% or higher. This means you need to answer at least 16 out of 20 questions correctly, or 40 out of 50 questions correctly, depending on your state's test format.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                Can I take the permit test online?
              </button>
            </h3>
            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#permitTestFAQ">
              <div class="accordion-body">
                Some states offer online permit testing, including New York, Florida, and California. Check with your state's DMV to see if online testing is available in your area.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                How much does the permit test cost?
              </button>
            </h3>
            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#permitTestFAQ">
              <div class="accordion-body">
                Permit test fees vary by state, typically ranging from $10 to $50. This fee usually includes the written test and your learner's permit if you pass.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                What happens if I fail the permit test?
              </button>
            </h3>
            <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#permitTestFAQ">
              <div class="accordion-body">
                If you fail the permit test, you can usually retake it after a waiting period (typically 1-7 days). You may need to pay an additional fee for each retake attempt.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Ready to Pass Your Permit Test?</h2>
        <p class="lead mb-4">Use our free permit test practice to prepare for your DMV test and pass on the first try</p>
        <a href="#permit-test-tool" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-car me-2"></i>Start Practice Test Now
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Permit Test Questions Database
const permitQuestions = [
  {
    id: 1,
    question: "What does a red traffic light mean?",
    options: ["Stop completely", "Slow down", "Proceed with caution", "Yield to oncoming traffic"],
    correct: 0,
    explanation: "A red traffic light means you must come to a complete stop and wait until the light turns green."
  },
  {
    id: 2,
    question: "When approaching a stop sign, you must:",
    options: ["Slow down and proceed if clear", "Stop completely", "Honk your horn", "Flash your lights"],
    correct: 1,
    explanation: "You must come to a complete stop at a stop sign, even if no other traffic is visible."
  },
  {
    id: 3,
    question: "What is the speed limit in a school zone when children are present?",
    options: ["15 mph", "20 mph", "25 mph", "30 mph"],
    correct: 1,
    explanation: "Most states set school zone speed limits at 20 mph when children are present."
  },
  {
    id: 4,
    question: "When can you pass another vehicle on the right?",
    options: ["Never", "When the vehicle ahead is turning left", "Only on highways", "When traffic is heavy"],
    correct: 1,
    explanation: "You may pass on the right when the vehicle ahead is making a left turn and there's sufficient space."
  },
  {
    id: 5,
    question: "What should you do when you see a pedestrian in a crosswalk?",
    options: ["Speed up to pass quickly", "Honk your horn", "Stop and yield", "Flash your lights"],
    correct: 2,
    explanation: "You must always stop and yield the right-of-way to pedestrians in crosswalks."
  },
  {
    id: 6,
    question: "How far should you stay behind the vehicle in front of you?",
    options: ["1 second", "2 seconds", "3 seconds", "4 seconds"],
    correct: 2,
    explanation: "The 3-second rule is recommended for safe following distance in normal conditions."
  },
  {
    id: 7,
    question: "What does a yellow traffic light mean?",
    options: ["Speed up", "Stop if safe to do so", "Proceed normally", "Honk your horn"],
    correct: 1,
    explanation: "A yellow light means caution - stop if you can do so safely, otherwise proceed through the intersection."
  },
  {
    id: 8,
    question: "When parking uphill on a street with a curb, which way should you turn your wheels?",
    options: ["Away from the curb", "Toward the curb", "Straight ahead", "It doesn't matter"],
    correct: 0,
    explanation: "When parking uphill with a curb, turn wheels away from the curb so the car won't roll into traffic if brakes fail."
  },
  {
    id: 9,
    question: "What is the legal blood alcohol limit for drivers 21 and over?",
    options: ["0.05%", "0.08%", "0.10%", "0.12%"],
    correct: 1,
    explanation: "The legal blood alcohol limit for drivers 21 and over is 0.08% in all 50 states."
  },
  {
    id: 10,
    question: "When should you use your turn signal?",
    options: ["Only when other cars are present", "At least 100 feet before turning", "Only on highways", "Only at night"],
    correct: 1,
    explanation: "You should signal at least 100 feet before turning in urban areas and 300 feet on highways."
  },
  {
    id: 11,
    question: "What should you do if your brakes fail while driving?",
    options: ["Pump the brakes", "Use the parking brake gradually", "Shift to a lower gear", "All of the above"],
    correct: 3,
    explanation: "If brakes fail, pump the brakes, use parking brake gradually, and shift to lower gear to slow down."
  },
  {
    id: 12,
    question: "When is it illegal to pass another vehicle?",
    options: ["On a hill", "In a curve", "Near an intersection", "All of the above"],
    correct: 3,
    explanation: "It's illegal to pass on hills, curves, near intersections, or anywhere visibility is limited."
  },
  {
    id: 13,
    question: "What does a flashing red light mean?",
    options: ["Caution", "Stop, then proceed when safe", "Yield", "No entry"],
    correct: 1,
    explanation: "A flashing red light means the same as a stop sign - stop completely, then proceed when safe."
  },
  {
    id: 14,
    question: "When driving in fog, you should:",
    options: ["Use high beams", "Use low beams", "Use hazard lights", "Turn off all lights"],
    correct: 1,
    explanation: "Use low beams in fog. High beams reflect off the fog and reduce visibility."
  },
  {
    id: 15,
    question: "What is the minimum age to get a learner's permit in most states?",
    options: ["14", "15", "16", "17"],
    correct: 1,
    explanation: "Most states allow learner's permits at age 15, though some allow 14 with driver's education."
  },
  {
    id: 16,
    question: "When approaching an emergency vehicle with flashing lights, you should:",
    options: ["Speed up to get out of the way", "Move over or slow down", "Stop immediately", "Flash your lights"],
    correct: 1,
    explanation: "Move over to another lane if possible, or slow down when approaching emergency vehicles."
  },
  {
    id: 17,
    question: "What should you do at a four-way stop?",
    options: ["Go in order of arrival", "Largest vehicle goes first", "Turn right first", "Honk and proceed"],
    correct: 0,
    explanation: "At a four-way stop, vehicles proceed in the order they arrived. If simultaneous, yield to the right."
  },
  {
    id: 18,
    question: "When can you legally use a cell phone while driving?",
    options: ["Never", "Only hands-free", "Only for emergencies", "Depends on the state"],
    correct: 3,
    explanation: "Cell phone laws vary by state. Many allow hands-free use but prohibit handheld devices."
  },
  {
    id: 19,
    question: "What does a diamond-shaped sign indicate?",
    options: ["Warning", "Regulatory", "Guide", "Construction"],
    correct: 0,
    explanation: "Diamond-shaped signs are warning signs that alert drivers to potential hazards ahead."
  },
  {
    id: 20,
    question: "When should you check your blind spots?",
    options: ["Before changing lanes", "Before backing up", "Before merging", "All of the above"],
    correct: 3,
    explanation: "Always check blind spots before changing lanes, backing up, merging, or any lateral movement."
  },
  {
    id: 21,
    question: "What is the purpose of anti-lock brakes (ABS)?",
    options: ["Stop faster", "Prevent skidding", "Save fuel", "Reduce noise"],
    correct: 1,
    explanation: "ABS prevents wheels from locking up and skidding, allowing you to maintain steering control."
  },
  {
    id: 22,
    question: "When parking downhill, which way should you turn your wheels?",
    options: ["Away from curb", "Toward the curb", "Straight", "Either direction"],
    correct: 1,
    explanation: "When parking downhill, turn wheels toward the curb so the car won't roll into traffic."
  },
  {
    id: 23,
    question: "What should you do if you miss your exit on a highway?",
    options: ["Back up", "Make a U-turn", "Continue to next exit", "Stop and ask for directions"],
    correct: 2,
    explanation: "If you miss your exit, continue to the next exit. Never back up or make U-turns on highways."
  },
  {
    id: 24,
    question: "When is it safe to drive through a yellow light?",
    options: ["Always", "Never", "If you can't stop safely", "Only if no one is watching"],
    correct: 2,
    explanation: "Drive through a yellow light only if you cannot stop safely before the intersection."
  },
  {
    id: 25,
    question: "What does a white line on the road indicate?",
    options: ["Traffic moving in opposite directions", "Traffic moving in same direction", "No passing zone", "Parking area"],
    correct: 1,
    explanation: "White lines separate traffic moving in the same direction, while yellow lines separate opposing traffic."
  },
  {
    id: 26,
    question: "When should you use your hazard lights?",
    options: ["In heavy rain", "When your vehicle is disabled", "In construction zones", "When driving slowly"],
    correct: 1,
    explanation: "Use hazard lights when your vehicle is disabled or stopped on the roadway as a warning to other drivers."
  },
  {
    id: 27,
    question: "What is the recommended tire tread depth?",
    options: ["1/16 inch", "1/8 inch", "1/4 inch", "1/2 inch"],
    correct: 1,
    explanation: "Tires should be replaced when tread depth reaches 1/8 inch (2/32 inch) for safety."
  },
  {
    id: 28,
    question: "When approaching a railroad crossing, you should:",
    options: ["Speed up to cross quickly", "Stop and listen", "Honk your horn", "Flash your lights"],
    correct: 1,
    explanation: "Always stop, look, and listen before crossing railroad tracks, even if no train is visible."
  },
  {
    id: 29,
    question: "What should you do if you're being tailgated?",
    options: ["Speed up", "Brake suddenly", "Allow more following distance ahead", "Change lanes immediately"],
    correct: 2,
    explanation: "If being tailgated, increase your following distance ahead to create a safety cushion."
  },
  {
    id: 30,
    question: "When can you drive in a bike lane?",
    options: ["Never", "When turning", "When parking", "When traffic is heavy"],
    correct: 1,
    explanation: "You may briefly enter a bike lane when making a turn, but otherwise they're reserved for bicycles."
  },
  {
    id: 31,
    question: "What does a octagonal sign shape indicate?",
    options: ["Warning", "Stop", "Yield", "Speed limit"],
    correct: 1,
    explanation: "Octagonal (8-sided) signs are always stop signs."
  },
  {
    id: 32,
    question: "When should you replace your windshield wipers?",
    options: ["Every year", "When they streak", "Every 6 months", "When they make noise"],
    correct: 1,
    explanation: "Replace windshield wipers when they begin to streak, skip, or don't clear the windshield properly."
  },
  {
    id: 33,
    question: "What is the safest way to exit a highway?",
    options: ["Brake in the travel lane", "Signal early and use deceleration lane", "Exit quickly", "Stop before exiting"],
    correct: 1,
    explanation: "Signal early, move to the exit lane, then use the deceleration lane to slow down safely."
  },
  {
    id: 34,
    question: "When driving at night, you should:",
    options: ["Use high beams in the city", "Overdrive your headlights", "Reduce speed", "Follow closer"],
    correct: 2,
    explanation: "Reduce speed at night because your stopping distance may exceed your headlight range."
  },
  {
    id: 35,
    question: "What should you do if your car starts to skid?",
    options: ["Brake hard", "Steer in the opposite direction", "Steer in the direction you want to go", "Accelerate"],
    correct: 2,
    explanation: "If your car skids, steer gently in the direction you want the front of the car to go."
  },
  {
    id: 36,
    question: "When is it legal to make a U-turn?",
    options: ["Anywhere", "Never", "When safe and legal", "Only on highways"],
    correct: 2,
    explanation: "U-turns are legal when safe and not prohibited by signs, and when you can complete the turn safely."
  }
];

// Test State Management
let currentTest = {
  questions: [],
  currentQuestionIndex: 0,
  answers: [],
  startTime: null,
  timeLimit: null,
  timer: null,
  state: '',
  testType: ''
};

// Test Configuration
const testConfigs = {
  quick: { questionCount: 10, timeLimit: 10 * 60 }, // 10 minutes
  practice: { questionCount: 20, timeLimit: 20 * 60 }, // 20 minutes
  full: { questionCount: 36, timeLimit: 36 * 60 } // 36 minutes
};

// Start Permit Test
function startPermitTest() {
  const state = document.getElementById('stateSelect').value;
  const testType = document.getElementById('testType').value;

  if (!state) {
    alert('Please select your state first.');
    return;
  }

  // Initialize test
  currentTest.state = state;
  currentTest.testType = testType;
  currentTest.startTime = new Date();

  const config = testConfigs[testType];
  currentTest.timeLimit = config.timeLimit;

  // Randomly select questions
  currentTest.questions = getRandomQuestions(config.questionCount);
  currentTest.answers = new Array(currentTest.questions.length).fill(null);
  currentTest.currentQuestionIndex = 0;

  // Track analytics
  if (typeof gtag !== 'undefined') {
    gtag('event', 'permit_test_start', {
      'state': state,
      'test_type': testType,
      'question_count': config.questionCount
    });
  }

  // Show test interface
  document.getElementById('test-setup').style.display = 'none';
  document.getElementById('test-interface').style.display = 'block';

  // Start timer
  startTimer();

  // Load first question
  loadQuestion();

  // Scroll to test
  document.getElementById('test-interface').scrollIntoView({ behavior: 'smooth' });
}

// Get random questions from database
function getRandomQuestions(count) {
  const shuffled = [...permitQuestions].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Start test timer
function startTimer() {
  const timerElement = document.getElementById('timer');
  let timeRemaining = currentTest.timeLimit;

  currentTest.timer = setInterval(() => {
    timeRemaining--;

    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Change color when time is running low
    if (timeRemaining <= 60) {
      timerElement.className = 'badge bg-danger text-white fs-6';
    } else if (timeRemaining <= 300) {
      timerElement.className = 'badge bg-warning text-dark fs-6';
    }

    if (timeRemaining <= 0) {
      endTest();
    }
  }, 1000);
}

// Load current question
function loadQuestion() {
  const question = currentTest.questions[currentTest.currentQuestionIndex];
  const questionNumber = currentTest.currentQuestionIndex + 1;
  const totalQuestions = currentTest.questions.length;

  // Update question info
  document.getElementById('test-info').textContent = `Question ${questionNumber} of ${totalQuestions}`;
  document.getElementById('question-text').textContent = question.question;

  // Update progress bar
  const progress = (questionNumber / totalQuestions) * 100;
  document.getElementById('progress-bar').style.width = `${progress}%`;

  // Update score display
  const correctAnswers = currentTest.answers.filter((answer, index) =>
    answer !== null && answer === currentTest.questions[index].correct
  ).length;
  const answeredQuestions = currentTest.answers.filter(answer => answer !== null).length;
  document.getElementById('current-score').textContent = `${correctAnswers}/${answeredQuestions}`;

  // Load answer options
  const answersContainer = document.getElementById('answers-container');
  answersContainer.innerHTML = '';

  question.options.forEach((option, index) => {
    const isSelected = currentTest.answers[currentTest.currentQuestionIndex] === index;

    const answerDiv = document.createElement('div');
    answerDiv.className = 'form-check mb-3';
    answerDiv.innerHTML = `
      <input class="form-check-input" type="radio" name="answer" id="answer${index}"
             value="${index}" ${isSelected ? 'checked' : ''} onchange="selectAnswer(${index})">
      <label class="form-check-label" for="answer${index}">
        ${option}
      </label>
    `;
    answersContainer.appendChild(answerDiv);
  });

  // Update navigation buttons
  document.getElementById('prev-btn').disabled = currentTest.currentQuestionIndex === 0;
  document.getElementById('next-btn').disabled = currentTest.answers[currentTest.currentQuestionIndex] === null;
}

// Select an answer
function selectAnswer(answerIndex) {
  currentTest.answers[currentTest.currentQuestionIndex] = answerIndex;
  document.getElementById('next-btn').disabled = false;

  // Auto-advance after 30 seconds (user preference for mental age tests)
  setTimeout(() => {
    if (currentTest.currentQuestionIndex < currentTest.questions.length - 1) {
      nextQuestion();
    }
  }, 30000);
}

// Navigate to next question
function nextQuestion() {
  if (currentTest.currentQuestionIndex < currentTest.questions.length - 1) {
    currentTest.currentQuestionIndex++;
    loadQuestion();
  } else {
    endTest();
  }
}

// Navigate to previous question
function previousQuestion() {
  if (currentTest.currentQuestionIndex > 0) {
    currentTest.currentQuestionIndex--;
    loadQuestion();
  }
}

// End the test
function endTest() {
  clearInterval(currentTest.timer);

  // Calculate results
  const results = calculateResults();

  // Show results
  showResults(results);

  // Track completion
  if (typeof gtag !== 'undefined') {
    gtag('event', 'permit_test_complete', {
      'state': currentTest.state,
      'test_type': currentTest.testType,
      'score': results.percentage,
      'passed': results.passed
    });
  }
}

// Calculate test results
function calculateResults() {
  const totalQuestions = currentTest.questions.length;
  const answeredQuestions = currentTest.answers.filter(answer => answer !== null).length;
  const correctAnswers = currentTest.answers.filter((answer, index) =>
    answer !== null && answer === currentTest.questions[index].correct
  ).length;

  const percentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
  const passed = percentage >= 80; // Most states require 80% to pass

  const endTime = new Date();
  const timeTaken = Math.floor((endTime - currentTest.startTime) / 1000);
  const minutes = Math.floor(timeTaken / 60);
  const seconds = timeTaken % 60;

  return {
    totalQuestions,
    answeredQuestions,
    correctAnswers,
    incorrectAnswers: answeredQuestions - correctAnswers,
    percentage,
    passed,
    timeTaken: `${minutes}:${seconds.toString().padStart(2, '0')}`
  };
}

// Show test results
function showResults(results) {
  // Hide test interface
  document.getElementById('test-interface').style.display = 'none';

  // Show results card
  const resultsCard = document.getElementById('test-results');
  resultsCard.style.display = 'block';

  // Set header color based on pass/fail
  const header = document.getElementById('results-header');
  header.className = `card-header text-white ${results.passed ? 'bg-success' : 'bg-danger'}`;

  // Update results content
  document.getElementById('final-score').textContent = `${results.percentage}%`;
  document.getElementById('pass-status').textContent = results.passed ?
    'Congratulations! You Passed!' : 'You Need More Practice';
  document.getElementById('score-details').textContent =
    `You answered ${results.correctAnswers} out of ${results.totalQuestions} questions correctly.`;

  document.getElementById('correct-count').textContent = results.correctAnswers;
  document.getElementById('incorrect-count').textContent = results.incorrectAnswers;
  document.getElementById('time-taken').textContent = results.timeTaken;

  // Scroll to results
  resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Review answers
function reviewAnswers() {
  document.getElementById('test-results').style.display = 'none';

  const reviewCard = document.getElementById('answer-review');
  const reviewContainer = document.getElementById('review-container');

  reviewContainer.innerHTML = '';

  currentTest.questions.forEach((question, index) => {
    const userAnswer = currentTest.answers[index];
    const isCorrect = userAnswer === question.correct;

    const reviewItem = document.createElement('div');
    reviewItem.className = `card mb-3 border-${isCorrect ? 'success' : 'danger'}`;
    reviewItem.innerHTML = `
      <div class="card-header ${isCorrect ? 'bg-success' : 'bg-danger'} text-white">
        <strong>Question ${index + 1}</strong>
        <span class="badge bg-light text-dark ms-2">${isCorrect ? 'Correct' : 'Incorrect'}</span>
      </div>
      <div class="card-body">
        <h6>${question.question}</h6>
        <div class="row">
          <div class="col-md-6">
            <p><strong>Your Answer:</strong> ${userAnswer !== null ? question.options[userAnswer] : 'Not answered'}</p>
            <p><strong>Correct Answer:</strong> ${question.options[question.correct]}</p>
          </div>
          <div class="col-md-6">
            <p><strong>Explanation:</strong> ${question.explanation}</p>
          </div>
        </div>
      </div>
    `;
    reviewContainer.appendChild(reviewItem);
  });

  reviewCard.style.display = 'block';
  reviewCard.scrollIntoView({ behavior: 'smooth' });
}

// Retake test
function retakeTest() {
  // Reset all displays
  document.getElementById('test-interface').style.display = 'none';
  document.getElementById('test-results').style.display = 'none';
  document.getElementById('answer-review').style.display = 'none';
  document.getElementById('test-setup').style.display = 'block';

  // Reset test state
  currentTest = {
    questions: [],
    currentQuestionIndex: 0,
    answers: [],
    startTime: null,
    timeLimit: null,
    timer: null,
    state: '',
    testType: ''
  };

  // Scroll to setup
  document.getElementById('test-setup').scrollIntoView({ behavior: 'smooth' });
}

// Share results
function shareResults() {
  const results = calculateResults();
  const shareText = `I scored ${results.percentage}% on my permit test practice! ${results.passed ? 'I passed!' : 'Time to study more.'} Try it yourself at PermitTestPro.com`;

  if (navigator.share) {
    navigator.share({
      title: 'My Permit Test Results',
      text: shareText,
      url: window.location.href
    });
  } else {
    navigator.clipboard.writeText(shareText + ' ' + window.location.href)
      .then(() => {
        alert('Results copied to clipboard!');
      })
      .catch(() => {
        alert('Share text: ' + shareText);
      });
  }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
  // Add smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth'
        });
      }
    });
  });

  // Initialize tooltips if Bootstrap is available
  if (typeof bootstrap !== 'undefined') {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
});
</script>
{% endblock %}